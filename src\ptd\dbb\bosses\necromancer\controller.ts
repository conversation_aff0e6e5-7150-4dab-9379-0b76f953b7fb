import { Entity } from "@minecraft/server";
import { getDistance } from "../../utilities/vector3";
import { executeCataclysmAttack } from "./attacks/cataclysm";
import { executeBookOfTheDamnedAttack } from "./attacks/bookOfTheDamned";
import { executeSoulDrainAttack } from "./attacks/soulDrain";
import { executePhantomPhaseAbility } from "./abilities/phantomPhase";
import { executeUndeadSummonAttack } from "./attacks/undeadSummon";
import { executeArcaneBlastAttack } from "./attacks/arcaneBlast";
import { executeSoulHandsAttack } from "./attacks/soulHands";
import { stopNecromancerSounds } from "./soundManager";
import {
  getAvailableAttacks,
  updateAttackHistory,
  displayAttackHistory,
  SHORT_RANGE_ATTACKS,
  MEDIUM_RANGE_ATTACKS,
  LONG_RANGE_ATTACKS,
  UNREACHABLE_RANGE_ATTACKS
} from "./attackTracker";
import { getTarget } from "../general_mechanics/targetUtils";

/**
 * Attack ranges for different attack types
 */
export const ATTACK_RANGES = {
  close: { min: 0, max: 8 },
  medium: { min: 8, max: 16 },
  long: { min: 16, max: 32 },
  unreachable: { min: 32, max: 64 }
};

/**
 * Attack durations in ticks
 */
export const ATTACK_DURATIONS: Record<string, number> = {
  cataclysm: 200, // 10 seconds at 20 ticks per second
  book_of_the_damned: 200, // 10 seconds at 20 ticks per second (same as cataclysm)
  soul_drain: 116, // Total attack duration is 116 ticks
  phantom_phase_start: 37, // Updated to match animation length - 9 extra ticks for smooth transition to the next animation
  phantom_phase_end: 95, // Updated to offset the animation length
  undead_summon: 115, // Total attack duration is 115 ticks
  arcane_blast: 100,
  soul_hands: 90
};

/**
 * Attack timing points in ticks
 * When the damage and effects should be applied
 */
export const ATTACK_TIMINGS: Record<string, number> = {
  cataclysm_start: 70, // 3.5 seconds
  cataclysm_end: 150, // 7.5 seconds
  book_of_the_damned_start: 70, // 3.5 seconds (same as cataclysm)
  book_of_the_damned_end: 150, // 7.5 seconds (same as cataclysm)
  soul_drain: 42, // 2.1 seconds
  phantom_phase_start: 37, // Teleport at the actual end of phase_start
  phantom_phase_end: 0, // No timing
  undead_summon: 40, // Start summoning zombies at 40 ticks
  arcane_blast: 50, // Fire skull at 50 ticks
  soul_hands: 45 // Summon skeleton souls at 45 ticks
};

/**
 * Sound effects for each attack type
 */
export const ATTACK_SOUND_MAP: Record<string, string> = {
  cataclysm: "mob.ptd_dbb_necromancer.cataclysm",
  book_of_the_damned: "mob.ptd_dbb_necromancer.cataclysm",
  soul_drain: "mob.ptd_dbb_necromancer.soul_drain",
  phantom_phase_start: "mob.ptd_dbb_necromancer.phantom_phase",
  phantom_phase_end: "mob.ptd_dbb_necromancer.phantom_phase",
  undead_summon: "mob.ptd_dbb_necromancer.undead_summon",
  arcane_blast: "mob.ptd_dbb_necromancer.arcane_blast",
  soul_hands: "mob.ptd_dbb_necromancer.soul_hands"
};

/**
 * Selects an attack based on target distance
 * @param necromancer The necromancer entity
 * @param target The target entity
 */
export function selectAttack(necromancer: Entity, target: Entity): void {
  // Check if currently cooling down
  const coolingDown = necromancer.getProperty("ptd_dbb:cooling_down") as boolean;
  if (coolingDown) {
    return; // Don't select an attack if cooling down
  }

  const distance = getDistance(necromancer.location, target.location);
  let selectedAttack = null;

  // Short range (0-5 blocks): select from available attacks based on usage history
  if (distance >= ATTACK_RANGES.close.min && distance <= ATTACK_RANGES.close.max) {
    // Get available attacks based on usage history
    const availableAttacks = getAvailableAttacks(necromancer, SHORT_RANGE_ATTACKS);
    if (availableAttacks.length > 0) {
      // Randomly select one of the available attacks
      const randomIndex = Math.floor(Math.random() * availableAttacks.length);
      const attack: string = availableAttacks[randomIndex]!;
      selectedAttack = attack;

      // Stop all other sound effects except for this attack's sound
      const attackSound = ATTACK_SOUND_MAP[attack];
      stopNecromancerSounds(necromancer, target, attackSound);

      // Trigger the attack event
      necromancer.triggerEvent(`ptd_dbb:${attack}_attack`);

      // Set the attack property and execute the attack immediately
      necromancer.setProperty("ptd_dbb:attack", attack);
      executeSelectedAttack(necromancer, attack, target);

      // Update attack history
      updateAttackHistory(necromancer, attack);
      // Display attack history on the actionbar
      displayAttackHistory(necromancer);
    }
  }
  // Medium range (5-10 blocks): select from available attacks based on usage history
  else if (distance > ATTACK_RANGES.medium.min && distance <= ATTACK_RANGES.medium.max) {
    // Get available attacks based on usage history
    const availableAttacks = getAvailableAttacks(necromancer, MEDIUM_RANGE_ATTACKS);
    if (availableAttacks.length > 0) {
      // Randomly select one of the available attacks
      const randomIndex = Math.floor(Math.random() * availableAttacks.length);
      const attack: string = availableAttacks[randomIndex]!;
      selectedAttack = attack;

      // Stop all other sound effects except for this attack's sound
      const attackSound = ATTACK_SOUND_MAP[attack];
      stopNecromancerSounds(necromancer, target, attackSound);

      // Trigger the attack event
      necromancer.triggerEvent(`ptd_dbb:${attack}_attack`);

      // Set the attack property and execute the attack immediately
      necromancer.setProperty("ptd_dbb:attack", attack);
      executeSelectedAttack(necromancer, attack, target);

      // Update attack history
      updateAttackHistory(necromancer, attack);
      // Display attack history on the actionbar
      displayAttackHistory(necromancer);
    }
  }
  // Long range (10-16 blocks): select from available attacks based on usage history
  else if (distance > ATTACK_RANGES.long.min && distance <= ATTACK_RANGES.long.max) {
    // Get available attacks based on usage history
    const availableAttacks = getAvailableAttacks(necromancer, LONG_RANGE_ATTACKS);
    if (availableAttacks.length > 0) {
      // Randomly select one of the available attacks
      const randomIndex = Math.floor(Math.random() * availableAttacks.length);
      const attack: string = availableAttacks[randomIndex]!;
      selectedAttack = attack;

      // Stop all other sound effects except for this attack's sound
      const attackSound = ATTACK_SOUND_MAP[attack];
      stopNecromancerSounds(necromancer, target, attackSound);

      // Trigger the attack event
      necromancer.triggerEvent(`ptd_dbb:${attack}_attack`);

      // Set the attack property and execute the attack immediately
      necromancer.setProperty("ptd_dbb:attack", attack);
      executeSelectedAttack(necromancer, attack, target);

      // Update attack history
      updateAttackHistory(necromancer, attack);
      // Display attack history on the actionbar
      displayAttackHistory(necromancer);
    }
  }
  // Unreachable range (16-32 blocks): select from available attacks based on usage history
  else if (distance > ATTACK_RANGES.unreachable.min && distance <= ATTACK_RANGES.unreachable.max) {
    // Get available attacks based on usage history
    const availableAttacks = getAvailableAttacks(necromancer, UNREACHABLE_RANGE_ATTACKS);
    if (availableAttacks.length > 0) {
      // Randomly select one of the available attacks
      const randomIndex = Math.floor(Math.random() * availableAttacks.length);
      const attack: string = availableAttacks[randomIndex]!;
      selectedAttack = attack;

      // Stop all other sound effects except for this attack's sound
      const attackSound = ATTACK_SOUND_MAP[attack];
      stopNecromancerSounds(necromancer, target, attackSound);

      // Trigger the attack event
      necromancer.triggerEvent(`ptd_dbb:${attack}_attack`);

      // Set the attack property and execute the attack immediately
      necromancer.setProperty("ptd_dbb:attack", attack);
      executeSelectedAttack(necromancer, attack, target);

      // Update attack history
      updateAttackHistory(necromancer, attack);
      // Display attack history on the actionbar
      displayAttackHistory(necromancer);
    }
  }


}

/**
 * Executes the selected attack using event-driven timing system
 * @param necromancer The necromancer entity
 * @param attack The attack type to execute
 * @param target The target entity (optional)
 */
export function executeSelectedAttack(necromancer: Entity, attack: string, target?: Entity): void {
  // Apply slowness effect for the duration of the attack
  if (attack in ATTACK_DURATIONS) {
    const attackDuration = ATTACK_DURATIONS[attack];
    if (attackDuration) {
      // Apply slowness with amplifier 250 for the duration of the attack
      // This effectively prevents movement during the attack
      necromancer.addEffect("minecraft:slowness", attackDuration, { amplifier: 250, showParticles: false });
    }
  }

  // Execute the specific attack using runTimeout patterns
  switch (attack) {
    case "cataclysm":
      executeCataclysmAttack(necromancer);
      break;
    case "book_of_the_damned":
      executeBookOfTheDamnedAttack(necromancer);
      break;
    case "soul_drain":
      if (target) {
        executeSoulDrainAttack(necromancer, target);
      }
      break;
    case "phantom_phase_start":
    case "phantom_phase_end":
      executePhantomPhaseAbility(necromancer, attack);
      break;
    case "undead_summon":
      executeUndeadSummonAttack(necromancer);
      break;
    case "arcane_blast":
      executeArcaneBlastAttack(necromancer);
      break;
    case "soul_hands":
      executeSoulHandsAttack(necromancer);
      break;
    default:
      console.warn(`Unknown attack type: ${attack}`);
      necromancer.triggerEvent("ptd_dbb:reset_attack");
      break;
  }
}
