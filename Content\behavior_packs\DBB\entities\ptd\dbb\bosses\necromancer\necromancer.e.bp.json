{
  "format_version": "1.21.70",
  "minecraft:entity": {
    "description": {
      "identifier": "ptd_dbb:necromancer",
      "is_spawnable": true,
      "is_summonable": true,
      "properties": {
        "ptd_dbb:spawning": {
          "type": "bool",
          "client_sync": true,
          "default": true
        },
        "ptd_dbb:dead": {
          "type": "bool",
          "client_sync": true,
          "default": false
        },
        "ptd_dbb:attack": {
          "type": "enum",
          "client_sync": true,
          "default": "none",
          "values": [
            "none",
            "cataclysm",
            "book_of_the_damned",
            "soul_drain",
            "phantom_phase_start",
            "phantom_phase_end",
            "undead_summon",
            "arcane_blast",
            "soul_hands"
          ]
        },
        "ptd_dbb:cooling_down": {
          "type": "bool",
          "client_sync": true,
          "default": false
        }
      }
    },
    "component_groups": {
      "ptd_dbb:spawning": {
        "minecraft:is_collidable": {},
        "minecraft:timer": {
          "time": 5.34,
          "looping": false,
          "time_down_event": {
            "event": "ptd_dbb:on_spawn",
            "target": "self"
          }
        },
        "minecraft:damage_sensor": {
          "triggers": [
            {
              "cause": "all",
              "deals_damage": "no"
            }
          ]
        }
      },
      "ptd_dbb:default": {
        "minecraft:damage_sensor": {
          "triggers": [
            {
              "on_damage": {
                "filters": {
                  "test": "actor_health",
                  "subject": "self",
                  "operator": "<=",
                  "value": 10
                },
                "event": "ptd_dbb:dead",
                "target": "self"
              },
              "deals_damage": "no"
            },
            {
              "on_damage": {
                "filters": {
                  "test": "bool_property",
                  "subject": "self",
                  "domain": "ptd_dbb:dead",
                  "value": true
                }
              },
              "deals_damage": "no"
            },
            {
              "on_damage": {
                "filters": {
                  "test": "bool_property",
                  "subject": "self",
                  "domain": "ptd_dbb:spawning",
                  "value": true
                }
              },
              "deals_damage": "no"
            }
          ]
        }
      },
      "ptd_dbb:targeting": {
        "minecraft:behavior.nearest_prioritized_attackable_target": {
          "priority": 0,
          "must_see": true,
          "attack_interval": 1,
          "reselect_targets": true,
          "must_see_forget_duration": 0,
          "reevaluate_description": true,
          "entity_types": [
            {
              "priority": 0,
              "max_dist": 64,
              "filters": {
                "test": "is_family",
                "subject": "other",
                "value": "player"
              }
            },
            {
              "priority": 1,
              "max_dist": 64,
              "filters": {
                "all_of": [
                  {
                    "test": "is_family",
                    "subject": "other",
                    "value": "boss"
                  },
                  {
                    "test": "is_family",
                    "subject": "other",
                    "operator": "!=",
                    "value": "necromancer"
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_dbb:spawning",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_dbb:dead",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  }
                ]
              }
            },
            {
              "priority": 2,
              "max_dist": 64,
              "filters": {
                "all_of": [
                  {
                    "test": "is_family",
                    "subject": "other",
                    "value": "minion"
                  },
                  {
                    "test": "is_family",
                    "subject": "other",
                    "operator": "!=",
                    "value": "necromancer"
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_dbb:spawning",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  },
                  {
                    "test": "bool_property",
                    "domain": "ptd_dbb:dead",
                    "subject": "other",
                    "operator": "==",
                    "value": false
                  }
                ]
              }
            }
          ]
        }
      },
      "ptd_dbb:has_collision": {
        // Restores the collision box after phantom phase
        "minecraft:collision_box": {
          "width": 3,
          "height": 5.5
        }
      },
      "ptd_dbb:melee": {
        "minecraft:attack": {
          "damage": 0
        },
        "minecraft:movement": {
          "max": 0.15
        },
        "minecraft:behavior.random_look_around": {
          "priority": 5
        },
        "minecraft:behavior.random_stroll": {
          "priority": 4
        }
      },
      "ptd_dbb:phantom_phase": {
        // This is just to remove the shadow of the entity while on phantom phase
        "minecraft:collision_box": {
          "height": 0.01,
          "width": 0.01
        }
      },
      "ptd_dbb:dead": {
        "minecraft:is_collidable": {},
        "minecraft:movement": {
          "max": 0
        },
        "minecraft:navigation.walk": {
          "is_amphibious": false,
          "can_pass_doors": false,
          "can_walk": false,
          "can_swim": false,
          "can_sink": false,
          "avoid_sun": false
        },
        "minecraft:behavior.random_look_around": {
          "priority": 999999
        },
        "minecraft:behavior.random_stroll": {
          "priority": 999999
        },
        "minecraft:body_rotation_blocked": {}
      }
    },
    "events": {
      "minecraft:entity_spawned": {
        "add": {
          "component_groups": ["ptd_dbb:spawning"]
        }
      },
      "ptd_dbb:on_spawn": {
        "sequence": [
          {
            "remove": {
              "component_groups": ["ptd_dbb:spawning"]
            },
            "set_property": {
              "ptd_dbb:spawning": false
            }
          },
          {
            "add": {
              "component_groups": ["ptd_dbb:default", "ptd_dbb:targeting", "ptd_dbb:melee"]
            }
          }
        ]
      },
      "ptd_dbb:dead": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:dead": true
            }
          },
          {
            "remove": {
              "component_groups": ["ptd_dbb:targeting", "ptd_dbb:melee", "ptd_dbb:phantom_phase"]
            }
          },
          {
            "add": {
              "component_groups": [
                "ptd_dbb:dead",
                "ptd_dbb:default" // Re-initialize the damage sensor so it doesn't bypass the death mechanics
              ]
            }
          }
        ]
      },
      "ptd_dbb:reset_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "none",
              "ptd_dbb:cooling_down": true
            }
          }
        ]
      },
      "ptd_dbb:cataclysm_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "cataclysm"
            }
          }
        ]
      },
      "ptd_dbb:book_of_the_damned_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "book_of_the_damned"
            }
          }
        ]
      },
      "ptd_dbb:soul_drain_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "soul_drain"
            }
          }
        ]
      },
      "ptd_dbb:phantom_phase_start_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "phantom_phase_start"
            }
          },
          {
            "remove": {
              "component_groups": ["ptd_dbb:phantom_phase", "ptd_dbb:has_collision"]
            }
          },
          {
            "add": {
              "component_groups": [
                "ptd_dbb:phantom_phase",
                "ptd_dbb:default" // Re-initialize the damage sensor so it doesn't bypass the death mechanics
              ]
            }
          }
        ]
      },
      "ptd_dbb:phantom_phase_end_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "phantom_phase_end"
            }
          },
          {
            "remove": {
              "component_groups": ["ptd_dbb:phantom_phase"]
            },
            "add": {
              "component_groups": [
                "ptd_dbb:has_collision", // Restore the collision box
                "ptd_dbb:default" // Re-initialize the damage sensor so it doesn't bypass the death mechanics
              ]
            }
          }
        ]
      },
      "ptd_dbb:undead_summon_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "undead_summon"
            }
          }
        ]
      },
      "ptd_dbb:arcane_blast_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "arcane_blast"
            }
          }
        ]
      },
      "ptd_dbb:soul_hands_attack": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "soul_hands"
            }
          }
        ]
      },
      "ptd_dbb:on_load": {
        "sequence": [
          {
            "set_property": {
              "ptd_dbb:attack": "none"
            }
          },
          {
            "add": {
              "component_groups": ["ptd_dbb:default", "ptd_dbb:targeting"]
            }
          }
        ]
      }
    },
    "components": {
      "minecraft:type_family": {
        "family": ["necromancer", "boss"]
      },
      "minecraft:collision_box": {
        "width": 3,
        "height": 5.5
      },
      "minecraft:health": {
        "value": 1000,
        "max": 1000
      },
      "minecraft:boss": {
        "hud_range": 64,
        "name": "Necromancer",
        "should_darken_sky": false
      },
      "minecraft:damage_sensor": {
        "triggers": [
          {
            "on_damage": {
              "filters": {
                "test": "actor_health",
                "subject": "self",
                "operator": "<=",
                "value": 10
              },
              "event": "ptd_dbb:dead",
              "target": "self"
            },
            "deals_damage": "no"
          },
          {
            "on_damage": {
              "filters": {
                "test": "bool_property",
                "domain": "ptd_dbb:dead",
                "subject": "self",
                "value": true
              }
            },
            "deals_damage": "no"
          }
        ]
      },
			"minecraft:variable_max_auto_step": {
			  "base_value": 1.0625,
			  "jump_prevented_value": 1.0625
			},
      "minecraft:jump.static": {},
      "minecraft:movement.basic": {},
      "minecraft:navigation.walk": {
        "can_path_over_water": true,
        "can_pass_doors": true,
        "can_break_doors": false,
        "avoid_water": true
      },
      /*
       * This allows the entity to follow/go to the target without attacking
       * Since the attacks are handled in the scripts
       */
      "minecraft:behavior.melee_box_attack": {
        "priority": 2,
        "can_spread_on_fire": true,
        "speed_multiplier": 1,
        "horizontal_reach": 0,
        "cooldown_time": 999999999
      },
      "minecraft:behavior.float": {
        "priority": 0
      },
      "minecraft:knockback_resistance": {
        "value": 0.9
      },
      "minecraft:follow_range": {
        "value": 256,
        "max": 256
      },
      "minecraft:physics": {},
      "minecraft:is_stackable": {},
      "minecraft:floats_in_liquid": {},
      "minecraft:persistent": {},
      "minecraft:conditional_bandwidth_optimization": {}
    }
  }
}
