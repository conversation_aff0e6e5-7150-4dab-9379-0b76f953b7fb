import { applyDeathCameraShake, handleDeathMechanics } from "../general_mechanics/deathMechanics";
import { stopNecromancerSounds } from "./soundManager";
import { spawnItemFountain } from "../general_mechanics/itemFountain";
import { selectAttack } from "./controller";
import { getTarget } from "../general_mechanics/targetUtils";
/**
 * Handles the necromancer boss mechanics
 * Currently implements spawning, idle, and movement mechanics
 *
 * @param necromancer The necromancer entity
 */
export function necromancerMechanics(necromancer) {
    try {
        // Skip if entity is not valid
        if (!necromancer)
            return;
        // Handle death mechanics using the generalized function
        // If the entity is dead, this will handle all death-related behavior and return true
        if (handleDeathMechanics(necromancer, {
            // Configure death mechanics specific to the Necromancer
            duration: 150,
            xpOrbs: {
                count: 8,
                duration: 100,
                heightOffset: 2.25
            },
            // No drops here as we'll use a custom event to spawn the essence fountain
            drops: [],
            deathSound: "mob.ptd_dbb_necromancer.death",
            // Add custom event to spawn essence fountain at the beginning of death sequence
            customEvents: [
                {
                    tick: 1,
                    callback: (entity) => {
                        applyDeathCameraShake(necromancer, 150);
                        // Spawn 32 essence items in a fountain-like effect
                        spawnItemFountain(entity, "ptd_dbb:necromancer_essence", 32, {
                            heightOffset: 2.25,
                            particleEffect: "minecraft:large_explosion",
                            soundEffect: "random.pop",
                            minVerticalStrength: 0.1,
                            maxVerticalStrength: 0.3,
                            minHorizontalStrength: 0.05,
                            maxHorizontalStrength: 0.2
                        });
                    }
                }
            ],
            // Provide the sound stopping function
            stopSoundsFn: (entity, excludedSound) => stopNecromancerSounds(entity, undefined, excludedSound)
        }, 1) // Pass currentTick parameter (1 for first tick)
        ) {
            // If death mechanics were applied, return early
            return;
        }
        // Handle spawning animation
        const isSpawning = necromancer.getProperty("ptd_dbb:spawning");
        if (isSpawning) {
            // Spawning is handled by the entity timer component (5.34 seconds = 107 ticks)
            // No manual tick counting needed - the timer will trigger ptd_dbb:on_spawn event
            return; // Skip other mechanics while spawning
        }
        // Handle attack mechanics using event-driven system
        const attack = necromancer.getProperty("ptd_dbb:attack");
        const coolingDown = necromancer.getProperty("ptd_dbb:cooling_down");
        if (attack && attack !== "none") {
            // Attack is active - execution is handled by individual attack functions using runTimeout
            // No timer increment needed as attacks use event-driven patterns
            return;
        }
        else if (!coolingDown) {
            // No attack is active and not cooling down, handle attack selection
            const target = getTarget(necromancer, necromancer.location, 32, ["necromancer"]);
            if (target) {
                selectAttack(necromancer, target);
            }
        }
    }
    catch (error) {
        console.warn(`Error in necromancer mechanics: ${error}`);
    }
}
